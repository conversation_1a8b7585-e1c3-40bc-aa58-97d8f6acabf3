# NI-Compute Development Container

This directory contains a dedicated development container environment for NI-Compute projects. The dev container provides a consistent Linux-based development environment with CUDA support, allowing you to develop and test PyTorch 2.5.1 applications even on macOS Intel chips.

## 📁 Directory Structure

```
ni-compute-devenv/
├── .devcontainer/          # Dev container configuration
│   ├── devcontainer.json   # Main configuration
│   ├── setup.sh           # Environment setup script
│   ├── Dockerfile         # Custom Docker image
│   ├── docker-compose.yml # Multi-service setup
│   └── README.md          # This file
└── projects/              # Mounted projects appear here
    └── SN27/              # Your SN27 project (mounted)
```

## 🚀 Quick Start

### Prerequisites

1. **Docker Desktop** with GPU support (if you have an NVIDIA GPU)
2. **VS Code** with the "Dev Containers" extension installed
3. **Git** (for cloning the repository)

### Getting Started

1. **Clone or navigate to the dev environment**:
   ```bash
   cd /path/to/ni-compute-devenv
   code .
   ```

2. **Open in Dev Container**:
   - Open VS Code in the `ni-compute-devenv` folder
   - Open the command palette (`Cmd+Shift+P` on macOS, `Ctrl+Shift+P` on Windows/Linux)
   - Type "Dev Containers: Reopen in Container"
   - Select the command and wait for the container to build

3. **Your SN27 project will be automatically mounted** at `/workspaces/projects/SN27`

## 🛠️ What's Included

### Base Environment
- **Ubuntu 22.04** with CUDA 12.4 support
- **Python 3.12** with all project dependencies
- **PyTorch 2.5.1** with CUDA support
- **All project dependencies** from requirements.txt

### Development Tools
- **VS Code Extensions**:
  - Python language support
  - Pylint, Black, isort, Flake8
  - Jupyter notebooks
  - Pytest integration
  - Git integration
  - GitHub Copilot (if you have access)

### Pre-installed Packages
- `ipython` - Enhanced Python REPL
- `jupyter` - Jupyter notebooks
- `pytest` - Testing framework
- `black` - Code formatter
- `pre-commit` - Git hooks
- And many more development tools

## 📝 Usage

### Navigating to Your Project
```bash
# Quick navigation to SN27
sn27

# Or manually
cd /workspaces/projects/SN27
```

### Running Tests
```bash
# Run all tests (from anywhere)
run-tests

# Run tests with coverage
run-tests-cov

# Or navigate to project first
sn27
python -m pytest tests/ -v
```

### Running the Application
```bash
# Run miner (from anywhere)
run-miner

# Run validator (from anywhere)
run-validator

# Or navigate to project first
sn27
python neurons/miner.py
python neurons/validator.py
```

### Jupyter Development
```bash
# Start Jupyter Lab
jupyter lab --ip=0.0.0.0 --port=8888 --no-browser --allow-root

# Access at http://localhost:8888
```

### Useful Aliases
The container includes several helpful aliases:
- `sn27` → `cd /workspaces/projects/SN27` (quick navigation)
- `py` → `python`
- `gs` → `git status`
- `ga` → `git add`
- `gc` → `git commit`
- `run-tests` → Run tests from anywhere
- `run-miner` → Run miner from anywhere
- `run-validator` → Run validator from anywhere

## 🔧 Configuration Files

### `devcontainer.json`
Main configuration file that defines:
- Base Docker image (NVIDIA CUDA)
- VS Code extensions to install
- Port forwarding
- Environment variables
- Post-creation commands

### `setup.sh`
Initialization script that:
- Updates system packages
- Installs Python dependencies
- Sets up development tools
- Configures environment variables
- Creates useful aliases

### `Dockerfile` (Optional)
Custom Docker image definition for advanced scenarios.

### `docker-compose.yml` (Optional)
Multi-service setup including:
- Main development container
- Redis (for caching/pub-sub testing)
- PostgreSQL (for database testing)

## 🎮 GPU Support

The dev container is configured to use NVIDIA GPUs if available:
- CUDA 12.4 runtime
- All GPUs accessible (`--gpus=all`)
- Shared memory configured for ML workloads

To verify GPU access:
```bash
python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}')"
nvidia-smi  # If you have NVIDIA drivers on host
```

## 🐛 Troubleshooting

### Container Won't Start
1. Ensure Docker Desktop is running
2. Check if you have enough disk space
3. Try rebuilding: "Dev Containers: Rebuild Container"

### GPU Not Detected
1. Ensure NVIDIA drivers are installed on host
2. Docker Desktop must have GPU support enabled
3. On Linux, install `nvidia-container-toolkit`

### Python Dependencies Issues
1. Rebuild container: "Dev Containers: Rebuild Container"
2. Or manually run: `pip install -r requirements.txt`

### Port Conflicts
The container forwards these ports by default:
- 8000, 8080, 5000, 3000 (application ports)
- 8888 (Jupyter)

If you have conflicts, modify the `forwardPorts` in `devcontainer.json`.

## 🔄 Updating the Environment

### Adding New Dependencies
1. Add to `pyproject.toml` or `requirements.txt`
2. Rebuild container or run `pip install <package>`

### Modifying the Container
1. Edit `devcontainer.json` or `setup.sh`
2. Rebuild: "Dev Containers: Rebuild Container"

## 📚 Additional Resources

- [VS Code Dev Containers Documentation](https://code.visualstudio.com/docs/devcontainers/containers)
- [Docker Desktop GPU Support](https://docs.docker.com/desktop/gpu/)
- [NVIDIA Container Toolkit](https://docs.nvidia.com/datacenter/cloud-native/container-toolkit/install-guide.html)

## 🤝 Contributing

When contributing to the dev container configuration:
1. Test changes thoroughly
2. Update this README if needed
3. Consider backward compatibility
4. Document any new features or requirements
