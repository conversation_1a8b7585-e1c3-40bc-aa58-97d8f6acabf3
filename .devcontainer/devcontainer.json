{
	"name": "NI-Compute Development Environment",
	"image": "mcr.microsoft.com/devcontainers/python:3.12-bullseye",

	// Features to add to the dev container. More info: https://containers.dev/features.
	"features": {
		"ghcr.io/devcontainers/features/git:1": {},
		"ghcr.io/devcontainers/features/github-cli:1": {},
		"ghcr.io/devcontainers/features/docker-in-docker:2": {}
	},

	// Configure tool-specific properties.
	"customizations": {
		// Configure properties specific to VS Code.
		"vscode": {
			// Add the IDs of extensions you want installed when the container is created.
			"extensions": [
				"ms-python.python",
				"ms-python.pylint",
				"ms-python.black-formatter",
				"ms-python.isort",
				"ms-python.flake8",
				"ms-toolsai.jupyter",
				"ms-vscode.test-adapter-converter",
				"ms-python.pytest",
				"charliermarsh.ruff",
				"ms-vscode.vscode-json",
				"redhat.vscode-yaml",
				"ms-vscode.makefile-tools",
				"eamodio.gitlens",
				"github.copilot",
				"github.copilot-chat"
			],
			"settings": {
				"python.defaultInterpreterPath": "/usr/local/bin/python",
				"python.linting.enabled": true,
				"python.linting.pylintEnabled": true,
				"python.formatting.provider": "black",
				"python.linting.flake8Enabled": true,
				"python.testing.pytestEnabled": true,
				"python.testing.unittestEnabled": false,
				"python.testing.pytestArgs": [
					"projects/SN27/tests"
				],
				"files.exclude": {
					"**/__pycache__": true,
					"**/*.pyc": true
				}
			}
		}
	},

	// Use 'forwardPorts' to make a list of ports inside the container available locally.
	"forwardPorts": [8000, 8080, 5000, 3000, 8888],

	// Use 'postCreateCommand' to run commands after the container is created.
	"postCreateCommand": "bash .devcontainer/setup.sh",

	// Comment out to connect as root instead. More info: https://aka.ms/vscode-remote/containers/non-root.
	"remoteUser": "vscode",

	// Set environment variables
	"containerEnv": {
		"PYTHONPATH": "/workspaces/projects/SN27"
	},

	// Mount the Docker socket, SSH keys, and SN27 project
	"mounts": [
		"source=/var/run/docker.sock,target=/var/run/docker.sock,type=bind",
		"source=${localWorkspaceFolder}/../SN27,target=/workspaces/projects/SN27,type=bind,consistency=cached",
		"source=${localWorkspaceFolder}/.devcontainer,target=/workspaces/projects/.devcontainer,type=bind,consistency=cached",
		"source=${localEnv:HOME}/.ssh,target=/home/<USER>/.ssh,type=bind,consistency=cached"
	],

	// Configure container runtime options
	"runArgs": [
		"--shm-size=2g"
	],

	// Lifecycle scripts
	"initializeCommand": "echo 'Initializing NI-Compute dev container...'",
	"onCreateCommand": "echo 'Container created successfully! SN27 project mounted at /workspaces/projects/SN27'",

	// Working directory - start in the projects folder
	"workspaceFolder": "/workspaces/projects"
}
