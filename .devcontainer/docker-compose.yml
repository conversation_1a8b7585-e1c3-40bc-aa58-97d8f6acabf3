version: '3.8'

services:
  ni-compute-dev:
    build:
      context: .
      dockerfile: Dockerfile
    volumes:
      - ../SN27:/workspaces/projects/SN27:cached
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      - PYTHONPATH=/workspaces/projects/SN27
      - CUDA_VISIBLE_DEVICES=all
    ports:
      - "8000:8000"
      - "8080:8080"
      - "5000:5000"
      - "3000:3000"
      - "8888:8888"  # Jupyter
    runtime: nvidia
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
    shm_size: 2gb
    stdin_open: true
    tty: true
    command: sleep infinity

  # Optional: Redis for caching/pub-sub testing
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  # Optional: PostgreSQL for database testing
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: ni_compute_dev
      POSTGRES_USER: dev
      POSTGRES_PASSWORD: dev_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  redis_data:
  postgres_data:
