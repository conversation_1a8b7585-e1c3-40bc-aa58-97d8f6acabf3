#!/bin/bash

# Exit on any error
set -e

echo "🚀 Setting up NI-Compute development environment..."

# Update system packages
echo "📦 Updating system packages..."
sudo apt-get update
sudo apt-get install -y \
    build-essential \
    curl \
    wget \
    git \
    vim \
    htop \
    tree \
    jq \
    unzip \
    software-properties-common \
    apt-transport-https \
    ca-certificates \
    gnupg \
    lsb-release

# Install additional development tools
echo "🔧 Installing development tools..."
sudo apt-get install -y \
    make \
    cmake \
    pkg-config \
    libssl-dev \
    libffi-dev \
    libbz2-dev \
    libreadline-dev \
    libsqlite3-dev \
    libncurses5-dev \
    libncursesw5-dev \
    xz-utils \
    tk-dev \
    libxml2-dev \
    libxmlsec1-dev \
    libffi-dev \
    liblzma-dev

# Upgrade pip and install pip-tools
echo "🐍 Setting up Python environment..."
python -m pip install --upgrade pip
pip install pip-tools wheel setuptools

# Install project dependencies from SN27
echo "📚 Installing SN27 project dependencies..."
SN27_PATH="/workspaces/projects/SN27"
if [ -d "$SN27_PATH" ]; then
    cd "$SN27_PATH"
    if [ -f "requirements.txt" ]; then
        pip install -r requirements.txt
    else
        echo "⚠️  requirements.txt not found, installing from pyproject.toml..."
        pip install -e .
    fi

    # Install development dependencies
    echo "🛠️  Installing development dependencies..."
    if [ -f "requirements-dev.txt" ]; then
        pip install -r requirements-dev.txt
    else
        pip install -e ".[dev]"
    fi

    # Return to workspace root
    cd /workspaces/projects
else
    echo "⚠️  SN27 project not found at $SN27_PATH"
    echo "    Make sure to mount the SN27 folder when starting the container"
fi

# Install additional useful development tools
echo "🔍 Installing additional development tools..."
pip install \
    ipython \
    jupyter \
    notebook \
    jupyterlab \
    pre-commit \
    black \
    isort \
    flake8 \
    mypy \
    pytest \
    pytest-xdist \
    pytest-mock \
    pytest-cov \
    allure-pytest

# Set up pre-commit hooks if .pre-commit-config.yaml exists
if [ -f ".pre-commit-config.yaml" ]; then
    echo "🪝 Setting up pre-commit hooks..."
    pre-commit install
fi

# Create useful aliases
echo "⚡ Setting up aliases..."
cat >> ~/.bashrc << 'EOF'

# NI-Compute Development Aliases
alias ll='ls -alF'
alias la='ls -A'
alias l='ls -CF'
alias ..='cd ..'
alias ...='cd ../..'
alias grep='grep --color=auto'
alias fgrep='fgrep --color=auto'
alias egrep='egrep --color=auto'

# Python aliases
alias py='python'
alias pip='python -m pip'
alias pytest='python -m pytest'
alias jupyter='python -m jupyter'

# Git aliases
alias gs='git status'
alias ga='git add'
alias gc='git commit'
alias gp='git push'
alias gl='git log --oneline'
alias gd='git diff'

# Project specific (SN27)
alias sn27='cd /workspaces/projects/SN27'
alias run-miner='cd /workspaces/projects/SN27 && python neurons/miner.py'
alias run-validator='cd /workspaces/projects/SN27 && python neurons/validator.py'
alias run-tests='cd /workspaces/projects/SN27 && python -m pytest tests/ -v'
alias run-tests-cov='cd /workspaces/projects/SN27 && python -m pytest tests/ -v --cov=. --cov-report=term-missing'

EOF

# Set up SSH key permissions if SSH directory exists
if [ -d "/home/<USER>/.ssh" ]; then
    echo "🔑 Setting up SSH key permissions..."
    sudo chown -R vscode:vscode /home/<USER>/.ssh
    chmod 700 /home/<USER>/.ssh
    if [ -f "/home/<USER>/.ssh/id_rsa" ]; then
        chmod 600 /home/<USER>/.ssh/id_rsa
    fi
    if [ -f "/home/<USER>/.ssh/id_ed25519" ]; then
        chmod 600 /home/<USER>/.ssh/id_ed25519
    fi
    if [ -f "/home/<USER>/.ssh/config" ]; then
        chmod 600 /home/<USER>/.ssh/config
    fi
    if [ -f "/home/<USER>/.ssh/known_hosts" ]; then
        chmod 644 /home/<USER>/.ssh/known_hosts
    fi
    echo "✅ SSH keys configured successfully"
else
    echo "⚠️  SSH directory not found - SSH keys not mounted"
fi

# Note: CUDA environment setup removed since we're using CPU-only container

# Verify installations
echo "✅ Verifying installations..."
echo "Python version: $(python --version)"
echo "Pip version: $(pip --version)"
echo "PyTorch version: $(python -c 'import torch; print(torch.__version__)' 2>/dev/null || echo 'PyTorch not installed yet')"

# Check if PyTorch is working (CPU-only)
echo "🔍 Checking PyTorch availability..."
python -c "
import torch
print(f'PyTorch version: {torch.__version__}')
print(f'CUDA available: {torch.cuda.is_available()}')
print('Running in CPU-only mode - this is expected for this container')
" 2>/dev/null || echo "PyTorch not ready yet"

echo "🎉 Development environment setup complete!"
echo ""
echo "📝 Quick start commands:"
echo "  - Run tests: run-tests"
echo "  - Run miner: run-miner"
echo "  - Run validator: run-validator"
echo "  - Start Jupyter: jupyter lab --ip=0.0.0.0 --port=8888 --no-browser --allow-root"
echo ""
echo "🔧 To reload environment variables, run: source ~/.bashrc"
