name: allure-python-pytest

on:
  push:
    branches-ignore:
      - '!main'

jobs:
  autotests:
    name: Run tests and generate Allure Report
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -e .[dev] -r requirements.txt -r requirements-dev.txt

      - name: Install software
        run: sudo apt-get install -y chromium-browser

      - name: Run Test
        if: always()
        run: pytest --alluredir=allure-results
        continue-on-error: true

      - name: Get Allure history
        uses: actions/checkout@v2
        if: always()
        continue-on-error: true
        with:
          ref: gh-pages
          path: gh-pages

      - name: Allure Report action from marketplace
        uses: simple-elf/allure-report-action@master
        if: always()
        with:
          allure_results: allure-results
          allure_history: allure-history

      - name: Deploy report to Github Pages
        if: always()
        uses: peaceiris/actions-gh-pages@v2
        env:
          PERSONAL_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          PUBLISH_BRANCH: gh-pages
          PUBLISH_DIR: allure-history
