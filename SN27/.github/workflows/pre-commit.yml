name: pre-commit

on:
  pull_request:
  push:
    branches: ["dev", "main"]

jobs:
  pre-commit:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - uses: actions/setup-python@v5
      with:
        python-version: '3.12'
        cache: 'pip'
        cache-dependency-path: |
          **/pyproject.toml
          **/requirements*.txt
    - run: pip install -e .[dev] -r requirements.txt -r requirements-dev.txt
    - uses: pre-commit/action@v3.0.1
