# What is Bittensor?
Bittensor is a network where computers validate the work that other computers contribute to the network - the work what is most valuable to the collective will be rewarded

<PERSON><PERSON><PERSON> is a catalyst to the open-source developers and smaller AI research labs now have a financial incentive for fine-tuning open foundational models

Bittensor is a library of machine intelligence that continuously grows and shares knowledge amongst peers

# What is a subnet?

Bittensor is releasing its own language for creating incentive mechanisms. This allows developers to build incentive systems on Bittensor, tapping into our web of intelligence to develop markets of the developer’s choosings

Subnet 1, an incentive system for machine intelligence production, showcases the enormous potential of markets to procure huge amounts of resources. Releasing user-created subnets is set to create a cambrian explosion of additional resources into the Bittensor ecosystem

# Why should you care?

As an open-source developer, you now have the ability to write your own incentive mechanisms without creating an entirely new chain. By tapping into Bittensor’s network of intelligence, you can incentivize AI models from all over the world to perform tasks of your choosing (i.e., image generation, storage, compute access, etc.) - the possibilities are truly endless

The release of subnets also offers the potential to pull these tools into a shared network, making all the ingredients necessary to create intelligence available within one network, governed by one token

You get to play a vital role in helping bootstrap what could one day become one of the most powerful networks in the world - and you make money by doing so!

By incentivizing developers to create their own markets, Bittensor is set to become a  one-stop-shop for those seeking all the compute requirements for building unstoppable applications on top of an incentivized infrastructure

# Deeper dive
Check out the Bittensor about page [here](https://bittensor.com/about) for more details about what the bittensor paradigm is and why subnets are revolutionary technology.

Also see our [linktree](https://linktr.ee/opentensor) for more information.
