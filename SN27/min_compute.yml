version: '1.0'
roles:
  miner:
    compute_spec:
      cpu:
        min_cores: 4
        min_speed: 2.5GHz
        architecture: x86_64
      gpu:
        required: true
        min_vram: 24GB
        cuda_cores: 1024
        min_compute_capability: 7.0
      memory:
        min_ram: 24GB
        min_swap: 4GB
      storage:
        min_space: 100GB
        type: SSD
        iops: 1000
      os:
        name: Ubuntu
        version: ">=20.04"
    network:
      bandwidth:
        download: ">=100Mbps"
        upload: ">=20Mbps"

  validator:
    compute_spec:
      cpu:
        min_cores: 4
        min_speed: 2.0GHz
        architecture: x86_64
      gpu:
        required: false
      memory:
        min_ram: 8GB
        min_swap: 2GB
      storage:
        min_space: 50GB
        type: SSD
        iops: 500
      os:
        name: Ubuntu
        version: ">=20.04"
    network:
      bandwidth:
        download: ">=50Mbps"
        upload: ">=10Mbps"
